<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Information Form</title>
    <link rel="icon" type="image/jpeg" href="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQ0NFQ8ODisZHiUtNystKysrKysrKy0rKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK//AABEIABwAHAMBIgACEQEDEQH/xAAbAAACAQUAAAAAAAAAAAAAAAAHCAYAAQIEBf/EACkQAAEDBAECBQUBAAAAAAAAAAECAwQABREhBjFRBxIiQXEUI2GBkRP/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8AF0KDMuDxYt8OTLeCSotxmVOKCRjJwkE42N/kVU2DNt7qmZ8OTFdSASiQyptQB6HCgNGu14f3WZaeY2t2C7/mqRJaivekELaW4gKTv9b66pi3bdY5l+uTUgIkTZUJpEqM56klgKX5ddNkq/lAr0K1XK4R5EiBAkyWIwy8400VJbGM7I6aGa0wcjIpjrZwxjh1i5U3CfU5DmMrdZQvamsNqBST7jsevfuVwiDLCfigkPBJlkt3Jo07khfESL95oMoKiXkqSUZxvA2fkDOqJkbxP4yjm827KelCI9bmY6VfTKz50rcJ116KG6CmM1WBQTfh/iLLstom2S5h6bbHo7jbHlwXGCQQAMkZRvpnXt2qDR8tNBJxkVlirUH/2Q==">
    <link rel="shortcut icon" type="image/jpeg" href="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQ0NFQ8ODisZHiUtNystKysrKysrKy0rKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK//AABEIABwAHAMBIgACEQEDEQH/xAAbAAACAQUAAAAAAAAAAAAAAAAHCAYAAQIEBf/EACkQAAEDBAECBQUBAAAAAAAAAAECAwQABREhBjFRBxIiQXEUI2GBkRP/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8AF0KDMuDxYt8OTLeCSotxmVOKCRjJwkE42N/kVU2DNt7qmZ8OTFdSASiQyptQB6HCgNGu14f3WZaeY2t2C7/mqRJaivekELaW4gKTv9b66pi3bdY5l+uTUgIkTZUJpEqM56klgKX5ddNkq/lAr0K1XK4R5EiBAkyWIwy8400VJbGM7I6aGa0wcjIpjrZwxjh1i5U3CfU5DmMrdZQvamsNqBST7jsevfuVwiDLCfigkPBJlkt3Jo07khfESL95oMoKiXkqSUZxvA2fkDOqJkbxP4yjm827KelCI9bmY6VfTKz50rcJ116KG6CmM1WBQTfh/iLLstom2S5h6bbHo7jbHlwXGCQQAMkZRvpnXt2qDR8tNBJxkVlirUH/2Q==">
</head>
<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00cdb0;
    --secondary-color: #e21351;
    --accent-color: #ff6900;
    --text-dark: #2d3748;
    --text-light: #718096;
    --bg-primary: #f7fafc;
    --bg-secondary: #ffffff;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gradient-bg);
    min-height: 100vh;
    color: var(--text-dark);
    line-height: 1.6;
}

.app-container {
    display: flex;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
    padding: 2rem;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.sidebar h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    text-align: center;
}

.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.action-button:hover::before {
    left: 100%;
}

.btn-retrieve {
    background: linear-gradient(135deg, var(--primary-color), #00b399);
    color: white;
}

.btn-export {
    background: linear-gradient(135deg, var(--accent-color), #e55a00);
    color: white;
}

.btn-clear {
    background: linear-gradient(135deg, var(--secondary-color), #c70d42);
    color: white;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 600px;
}

.form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
}

.logo-section {
    text-align: center;
    margin-bottom: 2rem;
}

.logo-img {
    max-width: 280px;
    height: auto;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.form-title {
    font-size: 1.75rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-dark);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.form-input, .form-select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 205, 176, 0.1);
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: var(--text-light);
}

.submit-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), #00b399);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Data Display */
.data-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}

.close-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    z-index: 10;
}

.close-button:hover {
    background: #c70d42;
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th {
    background: linear-gradient(135deg, var(--primary-color), #00b399);
    color: white;
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: 0.875rem 0.75rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.data-table tbody tr {
    transition: background-color 0.2s ease;
}

.data-table tbody tr:hover {
    background-color: rgba(0, 205, 176, 0.05);
}

.data-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Password Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.password-modal {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    text-align: center;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.show .password-modal {
    transform: scale(1);
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.modal-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.modal-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 205, 176, 0.1);
}

.modal-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.modal-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-button.confirm {
    background: var(--primary-color);
    color: white;
}

.modal-button.cancel {
    background: var(--border-color);
    color: var(--text-dark);
}

.modal-button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.error-message {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    display: none;
}

/* Toast Notifications */
.toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    font-weight: 600;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 1000;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.export-toast {
    background: linear-gradient(135deg, var(--accent-color), #e55a00);
}

.no-data {
    text-align: center;
    padding: 3rem;
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .app-container {
        flex-direction: column;
        padding: 1rem;
        gap: 1rem;
    }
    
    .sidebar {
        width: 100%;
        position: static;
        order: 2;
    }
    
    .main-content {
        order: 1;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .form-container, .data-container {
        padding: 1.5rem;
        border-radius: 15px;
    }
    
    .sidebar {
        padding: 1.5rem;
        border-radius: 15px;
    }
    
    .logo-img {
        max-width: 200px;
    }
    
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .toast {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        transform: translateY(100px);
    }
    
    .toast.show {
        transform: translateY(0);
    }
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Success Animation */
@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.success-animation {
    animation: successPulse 0.3s ease;
}

/* Shake Animation for incorrect password */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}
</style>

<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>Actions</h2>
            <button type="button" class="action-button btn-retrieve" onclick="requestAdminAccess('retrieve')">
                📊 Retrieve Details
            </button>
            <button type="button" class="action-button btn-export" onclick="requestAdminAccess('export')">
                📥 Export Data
            </button>
            <button type="button" class="action-button btn-clear" onclick="requestAdminAccess('clear')">
                🗑️ Clear Data
            </button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Form Container -->
            <div class="form-container">
                <div class="logo-section">
                    <img src="https://webinsite.co.za/wp-content/uploads/2024/05/Inscape-Linear-logo-RGB_black-on-transparent-300dpi.png" alt="Inscape Logo" class="logo-img">
                </div>
                
                <h1 class="form-title">Student Information Form</h1>
                
                <form id="leadForm">
                    <div class="form-group">
                        <label for="name" class="form-label">First Name</label>
                        <input type="text" id="name" class="form-input" placeholder="Enter your first name" required>
                    </div>

                    <div class="form-group">
                        <label for="surname" class="form-label">Last Name</label>
                        <input type="text" id="surname" class="form-input" placeholder="Enter your last name" required>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" id="phone" class="form-input" placeholder="Enter your phone number" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" id="email" class="form-input" placeholder="Enter your email address" required>
                    </div>

                    <div class="form-group">
                        <label for="parent_email" class="form-label">Parent Email</label>
                        <input type="email" id="parent_email" class="form-input" placeholder="Enter parent's email address">
                    </div>
                    
                    <div class="form-group">
                        <label for="parent_phone" class="form-label">Parent Number</label>
                        <input type="tel" id="parent_phone" class="form-input" placeholder="Enter parent's phone number">
                    </div>
                    
                    <div class="form-group">
                        <label for="lead_interest" class="form-label">Which course are you interested in?</label>
                        <select id="lead_interest" class="form-select" required>
                            <option value="">Select a course</option>
                            <option value="Advanced Diploma in UX">Advanced Diploma in UX</option>
                            <option value="BA Digital Marketing and Communication">BA Digital Marketing and Communication</option>
                            <option value="Fashion Design">Fashion Design</option>
                            <option value="Graphic Design">Graphic Design</option>
                            <option value="Higher Certificate in Architectural Technology">Higher Certificate in Architectural Technology</option>
                            <option value="Higher Certificate in Design Techniques">Higher Certificate in Design Techniques</option>
                            <option value="Higher Certificate in Fashion Design">Higher Certificate in Fashion Design</option>
                            <option value="Higher Certificate in Interior Decorating">Higher Certificate in Interior Decorating</option>
                            <option value="Honours in Design">Honours in Design</option>
                            <option value="Ideation Design">Ideation Design</option>
                            <option value="Interior Design">Interior Design</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="current_grade" class="form-label">Current Grade</label>
                        <input type="text" id="current_grade" class="form-input" placeholder="Enter your current grade" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="school" class="form-label">School Name</label>
                        <input type="text" id="school" class="form-input" placeholder="Enter your school name" required>
                    </div>

                    <button type="submit" class="submit-button">
                        Submit Application
                    </button>
                </form>
            </div>

            <!-- Data Display Container -->
            <div id="leads" class="data-container" style="display: none;"></div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast" class="toast">✅ Student information saved successfully!</div>
    <div id="exportToast" class="toast export-toast">📥 Data exported successfully!</div>

    <!-- Password Modal -->
    <div id="passwordModal" class="modal-overlay">
        <div class="password-modal">
            <h3 class="modal-title">🔒 Admin Access Required</h3>
            <p style="color: var(--text-light); margin-bottom: 1.5rem;">Please enter the admin password to continue:</p>
            <input type="password" id="passwordInput" class="modal-input" placeholder="Enter password" autocomplete="off">
            <div id="errorMessage" class="error-message">Incorrect password. Please try again.</div>
            <div class="modal-buttons">
                <button class="modal-button cancel" onclick="closePasswordModal()">Cancel</button>
                <button class="modal-button confirm" onclick="verifyPassword()">Confirm</button>
            </div>
        </div>
    </div>

    <script>
        // Admin password - Change this to your desired password
        const ADMIN_PASSWORD = 'Inscape@2025';
        let pendingAction = null;

        function requestAdminAccess(action) {
            console.log('Requesting admin access for action:', action);
            pendingAction = action;
            const modal = document.getElementById('passwordModal');
            const passwordInput = document.getElementById('passwordInput');
            const errorMessage = document.getElementById('errorMessage');

            // Reset modal state
            passwordInput.value = '';
            errorMessage.style.display = 'none';

            // Show modal
            modal.classList.add('show');

            // Focus on password input
            setTimeout(() => passwordInput.focus(), 100);
        }

        function closePasswordModal() {
            const modal = document.getElementById('passwordModal');
            modal.classList.remove('show');
            pendingAction = null;
        }

        function verifyPassword() {
            const passwordInput = document.getElementById('passwordInput');
            const errorMessage = document.getElementById('errorMessage');
            const enteredPassword = passwordInput.value;

            if (enteredPassword === ADMIN_PASSWORD) {
                // Password correct - execute the pending action
                console.log('Password correct, executing action:', pendingAction);
                closePasswordModal();

                // Add a small delay to ensure modal is closed before executing action
                setTimeout(() => {
                    switch(pendingAction) {
                        case 'retrieve':
                            console.log('Calling retrieveDetails()');
                            retrieveDetails();
                            break;
                        case 'export':
                            console.log('Calling exportToExcel()');
                            exportToExcel();
                            break;
                        case 'clear':
                            console.log('Calling clearData()');
                            clearData();
                            break;
                        default:
                            console.log('Unknown action:', pendingAction);
                    }
                    pendingAction = null;
                }, 100);
            } else {
                // Password incorrect - show error
                errorMessage.style.display = 'block';
                passwordInput.value = '';
                passwordInput.focus();

                // Add shake animation to modal
                const modal = document.querySelector('.password-modal');
                modal.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => modal.style.animation = '', 500);
            }
        }

        // Allow Enter key to submit password
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('passwordInput');
            passwordInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyPassword();
                }
            });

            // Close modal when clicking outside
            const modal = document.getElementById('passwordModal');
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closePasswordModal();
                }
            });
        });

        function showToast(toastId = 'toast') {
            const toast = document.getElementById(toastId);
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }
        
        function saveDetails() {
            const formData = {
                name: document.getElementById('name').value,
                surname: document.getElementById('surname').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                parent_email: document.getElementById('parent_email').value,
                parent_phone: document.getElementById('parent_phone').value,
                lead_interest: document.getElementById('lead_interest').value,
                current_grade: document.getElementById('current_grade').value,
                school: document.getElementById('school').value,
                timestamp: new Date().toLocaleString()
            };

            let leads = JSON.parse(localStorage.getItem('leads')) || [];
            leads.push(formData);
            localStorage.setItem('leads', JSON.stringify(leads));

            // Add success animation to form
            const form = document.querySelector('.form-container');
            form.classList.add('success-animation');
            setTimeout(() => form.classList.remove('success-animation'), 300);

            showToast();

            // Clear form
            document.getElementById('leadForm').reset();
        }

        function retrieveDetails() {
            console.log('retrieveDetails() function called');
            const leads = JSON.parse(localStorage.getItem('leads'));
            const container = document.getElementById('leads');

            if (leads && leads.length > 0) {
                let html = `
                    <button class="close-button" onclick="closeDataTable()" title="Close table">×</button>
                    <h2 style="margin-bottom: 1.5rem; color: var(--text-dark); text-align: center;">
                        📋 Student Submissions (${leads.length})
                    </h2>
                    <div style="overflow-x: auto;">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Surname</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Parent Email</th>
                                    <th>Parent Phone</th>
                                    <th>Course Interest</th>
                                    <th>Grade</th>
                                    <th>School</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                leads.forEach(lead => {
                    html += `
                        <tr>
                            <td>${lead.name}</td>
                            <td>${lead.surname}</td>
                            <td>${lead.phone}</td>
                            <td>${lead.email}</td>
                            <td>${lead.parent_email || 'N/A'}</td>
                            <td>${lead.parent_phone}</td>
                            <td>${lead.lead_interest}</td>
                            <td>${lead.current_grade}</td>
                            <td>${lead.school}</td>
                            <td>${lead.timestamp || 'N/A'}</td>
                        </tr>
                    `;
                });
                
                html += '</tbody></table></div>';
                container.innerHTML = html;
                container.style.display = 'block';
                
                // Smooth scroll to data
                container.scrollIntoView({ behavior: 'smooth' });
            } else {
                container.innerHTML = `
                    <button class="close-button" onclick="closeDataTable()" title="Close table">×</button>
                    <div class="no-data">📄 No student applications found.</div>
                `;
                container.style.display = 'block';
            }
        }

        function exportToExcel() {
            console.log('exportToExcel() function called');
            const leads = JSON.parse(localStorage.getItem('leads'));
            
            if (leads && leads.length > 0) {
                let csvContent = "data:text/csv;charset=utf-8,";
                csvContent += "Name,Surname,Phone Number,Email,Parent Email,Parent Number,Lead Interest,Current Grade,School Name,Date Submitted\n";
                
                leads.forEach(row => {
                    const csvRow = [
                        row.name,
                        row.surname,
                        row.phone,
                        row.email,
                        row.parent_email || '',
                        row.parent_phone,
                        `"${row.lead_interest}"`,
                        row.current_grade,
                        `"${row.school}"`,
                        row.timestamp || ''
                    ].join(',');
                    csvContent += csvRow + "\n";
                });
                
                const encodedUri = encodeURI(csvContent);
                const link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", `inscape_leads_${new Date().toISOString().split('T')[0]}.csv`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                showToast('exportToast');
            } else {
                const exportToast = document.getElementById("exportToast");
                exportToast.innerHTML = "⚠️ No student applications to export.";
                showToast('exportToast');
                setTimeout(() => {
                    exportToast.innerHTML = "📥 Data exported successfully!";
                }, 3000);
            }
        }

        function clearData() {
            console.log('clearData() function called');
            if (confirm('Are you sure you want to clear all student data? This action cannot be undone.')) {
                localStorage.removeItem('leads');
                const container = document.getElementById('leads');
                container.innerHTML = `
                    <button class="close-button" onclick="closeDataTable()" title="Close table">×</button>
                    <div class="no-data">🗑️ All data has been cleared.</div>
                `;
                container.style.display = 'block';
            }
        }

        function closeDataTable() {
            const container = document.getElementById('leads');
            container.style.display = 'none';
            container.innerHTML = '';
        }

        // Form submission handler
        document.getElementById('leadForm').addEventListener('submit', function (event) {
            event.preventDefault();
            saveDetails();
        });

        // Add loading states to buttons
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', function() {
                this.classList.add('loading');
                setTimeout(() => this.classList.remove('loading'), 500);
            });
        });
    </script>
</body>
</html>