<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline Lead Form</title>
    <link rel="stylesheet" href="style.css">
    <script  src="script.js"></script>

    <link rel="icon" type="image/jpeg" href="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQ0NFQ8ODisZHiUtNystKysrKysrKy0rKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK//AABEIABwAHAMBIgACEQEDEQH/xAAbAAACAQUAAAAAAAAAAAAAAAAHCAYAAQIEBf/EACkQAAEDBAECBQUBAAAAAAAAAAECAwQABREhBjFRBxIiQXEUI2GBkRP/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8AF0KDMuDxYt8OTLeCSotxmVOKCRjJwkE42N/kVU2DNt7qmZ8OTFdSASiQyptQB6HCgNGu14f3WZaeY2t2C7/mqRJaivekELaW4gKTv9b66pi3bdY5l+uTUgIkTZUJpEqM56klgKX5ddNkq/lAr0K1XK4R5EiBAkyWIwy8400VJbGM7I6aGa0wcjIpjrZwxjh1i5U3CfU5DmMrdZQvamsNqBST7jsevfuVwiDLCfigkPBJlkt3Jo07khfESL95oMoKiXkqSUZxvA2fkDOqJkbxP4yjm827KelCI9bmY6VfTKz50rcJ116KG6CmM1WBQTfh/iLLstom2S5h6bbHo7jbHlwXGCQQAMkZRvpnXt2qDR8tNBJxkVlirUH/2Q==">
    
    <!-- Add the desktop icon (optional, generally same as favicon) -->
    <link rel="shortcut icon" type="image/jpeg" href="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQ0NFQ8ODisZHiUtNystKysrKysrKy0rKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK//AABEIABwAHAMBIgACEQEDEQH/xAAbAAACAQUAAAAAAAAAAAAAAAAHCAYAAQIEBf/EACkQAAEDBAECBQUBAAAAAAAAAAECAwQABREhBjFRBxIiQXEUI2GBkRP/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8AF0KDMuDxYt8OTLeCSotxmVOKCRjJwkE42N/kVU2DNt7qmZ8OTFdSASiQyptQB6HCgNGu14f3WZaeY2t2C7/mqRJaivekELaW4gKTv9b66pi3bdY5l+uTUgIkTZUJpEqM56klgKX5ddNkq/lAr0K1XK4R5EiBAkyWIwy8400VJbGM7I6aGa0wcjIpjrZwxjh1i5U3CfU5DmMrdZQvamsNqBST7jsevfuVwiDLCfigkPBJlkt3Jo07khfESL95oMoKiXkqSUZxvA2fkDOqJkbxP4yjm827KelCI9bmY6VfTKz50rcJ116KG6CmM1WBQTfh/iLLstom2S5h6bbHo7jbHlwXGCQQAMkZRvpnXt2qDR8tNBJxkVlirUH/2Q==">

</head>
<style>
    .container {
    display: flex;
}

.form {
    flex: 1;
    MAX-width: 400px; /* Maintain the form width */
    margin-top: 100px;
    margin-right: 20px; /* Add some space between the form and menu */
}

.sidebar {
    width: 200px;
    padding: 20px;
    background-color: #f0f0f0;
}
.clear{
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    color:white;
    border: none;
    background-color: #e21351;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s;
}
.retrieve{
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    color:white;
    border: none;
    background-color: #00cdb0;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s;
}
.export{
    display: block;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    color:white;
    border: none;
    background-color: #ff6900;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.3s;
}



        
 .logoImg{
            margin-top: -30px;
            margin-bottom: -10px;

            max-width: 280px;
            margin-left: -30px;
        }
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .form {
            width: 400px;
            margin-top: 100px;
        }

        .leadform {
            padding: 20px;
            border-radius: 10px;
            background-color: #ffffff;
            box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
        }

        .leadform label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
        }
        select{
            width: calc(100% - 20px); /* Adjusted width to accommodate padding */
            padding: 8px;
            border: 2px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            background-color: white ;
            margin-bottom: 10px;
            font-family: Arial, sans-serif; 
        }

        .leadform input[type="text"],
        .leadform input[type="number"],
        
        .leadform input[type="email"],
        .leadform input[type="tel"],
        .leadform input[type="submit"] {
            width: calc(100% - 20px); /* Adjusted width to accommodate padding */
            padding: 8px;
            border: 2px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            margin-bottom: 10px;
        }

        .leadform input[type="submit"] {
            background-color: #00cdb0;
            color: white;
            border: none;
            cursor: pointer;
        }

        .leadform input[type="submit"]:hover {
            background-color: #02c4aa;
        }
        table{
            margin-left: -360px;
        }
        @media only screen and (max-width: 768px) {
    table {
        width: 90%;
        margin-left: -360px;
        border-collapse: collapse; /* Ensure borders collapse properly */
    }

    th, td {
        /* Adjust padding for better spacing */
        text-align: left; /* Align text to the left */
        border: 1px solid #ddd; /* Add borders to cells */
        word-wrap: break-word; /* Allow long words to break and wrap onto the next line */
    }
}

        th, td {
            padding: 12px 18px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #ffffff;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #ffffff;
        }
        tr:nth-child(odd) {
            background-color: #ffffff;
        }

        tbody tr{
            background-color: #f2f2f2;
        }

        tbody tr:last-child {
            border-bottom: none;
        }

        .leadcontainer {
            width: 100%;
            margin: 15px auto; /* Center the table horizontally */
        }
        body{
            background-color: rgb(230, 230, 230);
        }

</style>
<body>
  
    <div class="sidebar">
        <button type="button" class="retrieve" onclick="retrieveDetails()">Retrieve Details</button>
        <button type="button"  class="export" onclick="exportToExcel()">Export Data</button>
        <button type="button" class="clear" onclick="clearData()">Clear Data</button>
    </div>
    <div class="form">
        
        <div class="leadform">
            
        
             <div class="logo">
                <img src="https://webinsite.co.za/wp-content/uploads/2024/05/Inscape-Linear-logo-RGB_black-on-transparent-300dpi.png" alt=" " class="logoImg">
            </div> 
            
            <form id="leadForm">
                
        

                <label for="name">First Name</label>
                <input type="text" id="name" required>

                <label for="surname">Last Name</label>
                <input type="text" id="surname" required>

                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" required>
                
                <label for="email">Email Address</label>
                <input type="email" id="email" required>

                <label for="parent_email">Parent Email</label>
                <input type="email" id="parent_email" >
                
                <label for="parent_phone">Parent Number</label>
                <input type="tel" id="parent_phone" required>
                
                <label for="lead_interest">Which course are you interested in?</label>
                <select  id="lead_interest" required>
                    <option value="">Select an option</option>
                    <option value="Advanced Diploma in UX">Advanced Diploma in UX</option>
                    <option value="BA Digital Marketing and Communication">BA Digital Marketing and Communication</option>
                    <option value="Fashion Design">Fashion Design</option>
                    <option value="Graphic Design">Graphic Design</option>
                    <option value="Higher Certificate in Architectural Technology">Higher Certificate in Architectural Technology</option>
                    <option value="Higher Certificate in Design Techniques">Higher Certificate in Design Techniques</option>
                    <option value="Higher Certificate in Fashion Design">Higher Certificate in Fashion Design</option>
                    <option value="Higher Certificate in Fashion Design">Higher Certificate in Interior Decorating</option>
                    <option value="Honours in Design">Honours in Design</option>
                    <option value="Ideation Design">Ideation Design</option>
                    <option value="Interior Design">Interior Design</option>
                    
            </select>
                
                <label for="current_grade">Current Grade</label>
                <input type="text" id="current_grade" required>
                
                <!-- <label for="school">School</label>
                <input type="text" id="school" required> -->

                <input type="submit" value="Submit">
              
            </form>
        </div>
     
        <div id="leads" class="leadcontainer"></div>
    </div>
   

<script>
    
    function saveDetails() {
            var name = document.getElementById('name').value;
            var surname = document.getElementById('surname').value;
            var phone = document.getElementById('phone').value;
            var email = document.getElementById('email').value;
            var parent_email = document.getElementById('parent_email').value;
            var parent_phone = document.getElementById('parent_phone').value;
            var lead_interest = document.getElementById('lead_interest').value;
            var current_grade = document.getElementById('current_grade').value;
           // var school = document.getElementById('school').value;

            var leads = JSON.parse(localStorage.getItem('leads')) || [];

            leads.push({
                name: name,
                surname: surname,
                phone: phone,
                email: email,
                parent_email: parent_email,
                parent_phone: parent_phone,
                lead_interest: lead_interest,
                current_grade: current_grade,
            //   school: school
            });
            localStorage.setItem('leads', JSON.stringify(leads));

            alert('Lead saved successfully.');

            document.getElementById('name').value="";
            document.getElementById('surname').value = '';
            document.getElementById('phone').value = '';
            document.getElementById('email').value = '';
            document.getElementById('parent_email').value;
            document.getElementById('parent_phone').value = '';
            document.getElementById('lead_interest').value = '';
            document.getElementById('current_grade').value = '';
        }

        function retrieveDetails() {
            var leads = JSON.parse(localStorage.getItem('leads'));
            if (leads && leads.length > 0) {
                var html = '<table><thead><tr><th>Name</th><th>Surname</th><th>Phone Number</th><th>Email</th><th>Parent Email</th><th>Parent Number</th><th>Lead Interest</th><th>Current Grade</th></tr></thead><tbody>';
                leads.forEach(function (lead) {
                    html += '<tr><td>' + lead.name + '</td><td>' + lead.surname + '</td><td>' + lead.phone + '</td><td>' + lead.email + '</td><td>' + lead.parent_email + '</td><td>' + lead.parent_phone + '</td><td>' + lead.lead_interest + '</td><td>' + lead.current_grade + '</td></tr>';
                });
                html += '</tbody></table>';
                document.getElementById('leads').innerHTML = html;
            } else {
                document.getElementById('leads').innerHTML = 'No details found.';
            }
        }

        function exportToExcel() {
            var leads = JSON.parse(localStorage.getItem('leads'));
            if (leads && leads.length > 0) {
                var csvContent = "data:text/csv;charset=utf-8,"
                    + "Name,Surname,Phone Number,Email,Parent Email,Parent Number,Lead Interest,Current Grade,\n";
                leads.forEach(function (row) {
                    csvContent += row.name + "," + row.surname + "," + row.phone + "," + row.email + "," + row.parent_email + "," + row.parent_phone + "," + row.lead_interest + "," + row.current_grade + ","  + "\n";
                });
                var encodedUri = encodeURI(csvContent);
                var link = document.createElement("a");
                link.setAttribute("href", encodedUri);
                link.setAttribute("download", "leads.csv");
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert('No leads to export.');
            }
        }

        function clearData() {
            localStorage.removeItem('leads');
            document.getElementById('leads').innerHTML = 'Data cleared.';
        }

        document.getElementById('leadForm').addEventListener('submit', function (event) {
            event.preventDefault();
            saveDetails();
        });

    
</script>  

</body>

</html>